<template>
  <div class="search-form-container">
    <!-- <a-form layout="inline" :model="formState" @finish="handleSubmit"> -->
    <a-form layout="inline" :model="formState">
      <div class="search-form-content">
        <!-- 左侧搜索区域 -->
        <div class="search-left">
          <a-row :gutter="16">
            <template v-for="(col, index) in leftFields" :key="index">
              <!-- Select dropdown -->
              <a-col class="search-col" v-if="col.type === 'select'" :span="col.span || 6">
                <a-form-item :name="col.prop" :label="col.formItem?.label">
                  <a-select
                    v-bind="col.attrs"
                    :size="col.attrs?.size"
                    v-model:value="formState[col.prop]"
                    :mode="col.mode"
                    @change="handleFieldChange(col, $event)"
                    @clear="handleFieldClear(col)"
                  >
                    <a-select-option
                      v-for="(option, optIndex) in col.options"
                      :key="optIndex"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <!-- Input col -->
              <a-col v-else-if="col.type === 'input'" :span="col.span || 6">
                <a-form-item :name="col.prop" :label="col.formItem?.label">
                  <a-input
                    v-model:value="formState[col.prop]"
                    :placeholder="col.attrs?.placeholder || `请输入${col.formItem?.label}`"
                    :allowClear="col.attrs?.clearable !== false"
                    @change="handleFieldChange(col, $event)"
                  />
                </a-form-item>
              </a-col>

              <!-- Date picker -->
              <a-col v-else-if="col.type === 'datePicker'" :span="col.span || 6">
                <a-form-item :name="col.prop" :label="col.formItem?.label">
                  <a-date-picker
                    v-model:value="formState[col.prop]"
                    :placeholder="col.attrs?.placeholder || `请选择${col.formItem?.label}`"
                    :allowClear="col.attrs?.clearable !== false"
                    :format="col.attrs?.format || 'YYYY-MM-DD'"
                    :valueFormat="col.attrs?.valueFormat || 'YYYY-MM-DD'"
                    @change="handleFieldChange(col, $event)"
                  />
                </a-form-item>
              </a-col>
              <!-- Date range picker -->
              <a-col v-else-if="col.type === 'rangePicker'" :span="col.span || 6">
                <a-form-item :name="col.prop" :label="col.formItem?.label">
                  <a-range-picker
                    v-model:value="formState[col.prop]"
                    v-bind="col.attrs"
                    :allowClear="col.attrs?.clearable !== false"
                    @change="handleFieldChange(col, $event)"
                  />
                </a-form-item>
              </a-col>

              <!-- radio -->
              <a-col v-else-if="col.type === 'radio'" :span="col.span || 6">
                <a-form-item :name="col.prop" :label="col.formItem?.label">
                  <a-radio-group
                    v-model:value="formState[col.prop]"
                    :button-style="col.attrs?.buttonStyle || 'solid'"
                    :size="col.attrs?.size || 'large'"
                    @change="handleFieldChange(col, $event)"
                  >
                    <a-radio-button
                      v-for="(option, optIndex) in col.options"
                      :key="optIndex"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </template>
          </a-row>
        </div>

        <!-- 右侧区域 -->
        <div class="search-right">
          <!-- 使用fields中配置的右侧表单项 -->
          <template v-for="(col, index) in rightFields" :key="`right-${index}`">
            <!-- radio -->
            <div v-if="col.type === 'radio'" class="right-field-item">
              <a-radio-group
                v-model:value="formState[col.prop]"
                :button-style="col.attrs?.buttonStyle || 'solid'"
                :size="col.attrs?.size || 'large'"
                @change="handleFieldChange(col, $event)"
              >
                <a-radio-button
                  v-for="(option, optIndex) in col.options"
                  :key="optIndex"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-radio-button>
              </a-radio-group>
            </div>
            <!-- Select dropdown -->
            <!-- <div v-if="col.type === 'select'" class="right-field-item">
              <a-form-item :name="col.prop" :label="col.formItem?.label">
                <a-select
                  v-bind="col.attrs"
                  :size="col.attrs?.size"
                  v-model:value="formState[col.prop]"
                  :mode="col.mode"
                  @change="handleFieldChange(col, $event)"
                  @clear="handleFieldClear(col)"
                >
                  <a-select-option
                    v-for="(option, optIndex) in col.options"
                    :key="optIndex"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </div> -->

            <!-- Input -->
            <!-- <div v-else-if="col.type === 'input'" class="right-field-item">
              <a-form-item :name="col.prop" :label="col.formItem?.label">
                <a-input
                  v-model:value="formState[col.prop]"
                  :placeholder="col.attrs?.placeholder || `请输入${col.formItem?.label}`"
                  :allowClear="col.attrs?.clearable !== false"
                  @change="handleFieldChange(col, $event)"
                />
              </a-form-item>
            </div> -->

            <!-- Date picker -->
            <!-- <div v-else-if="col.type === 'datePicker'" class="right-field-item">
              <a-form-item :name="col.prop" :label="col.formItem?.label">
                <a-date-picker
                  v-model:value="formState[col.prop]"
                  :placeholder="col.attrs?.placeholder || `请选择${col.formItem?.label}`"
                  :allowClear="col.attrs?.clearable !== false"
                  :format="col.attrs?.format || 'YYYY-MM-DD'"
                  :valueFormat="col.attrs?.valueFormat || 'YYYY-MM-DD'"
                  @change="handleFieldChange(col, $event)"
                />
              </a-form-item>
            </div> -->
          </template>
        </div>
      </div>

      <!-- 操作按钮 -->
      <!-- <div class="search-actions">
        <a-button type="primary" html-type="submit">查询</a-button>
        <a-button style="margin-left: 8px" @click="resetForm">重置</a-button>
      </div> -->
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import { cloneDeep } from "lodash";

interface FieldConfig {
  type: "input" | "select" | "datePicker" | "rangePicker" | "radio";
  prop: string;
  formItem: {
    label: string;
  };
  attrs?: {
    size?: string;
    buttonStyle?: "outline" | "solid";
    placeholder?: any;
    clearable?: boolean;
    fieldNames?: any;
    format?: any;
    valueFormat?: any;
  };
  span?: number;
  mode?: "multiple" | "tags";
  options?: Array<any>;
  defaultValue?: any;
  showRight?: boolean; // 新增：是否右侧对齐
  on?: {
    change?: (val: any) => void;
    clear?: () => void;
  };
}

const props = defineProps<{
  fields: FieldConfig[];
  initialValues?: Record<string, any>;
  defaultTimeRange?: "day" | "month" | "year";
  showSearchBox?: boolean;
  showTimeRange?: boolean;
}>();

// const emit = defineEmits<{
//   (e: "search", values: Record<string, any>): void;
//   (e: "reset"): void;
//   (e: "timeRangeChange", timeRange: string): void;
// }>();

// 表单状态
const formState = reactive<Record<string, any>>({});
// 时间范围
// const timeRange = ref(props.defaultTimeRange || "year");
// 深拷贝的字段配置
const fieldsClone = ref(cloneDeep(props.fields));

// 计算属性：分离左侧和右侧字段
const leftFields = computed(() => {
  return props.fields.filter((field) => !field.showRight);
});

const rightFields = computed(() => {
  return props.fields.filter((field) => field.showRight);
});

// 监听fields变化，更新克隆的字段配置
watch(
  () => props.fields,
  (newFields) => {
    fieldsClone.value = cloneDeep(newFields);
    // 在这里可以做数据处理
    // console.log("字段配置已更新:", fieldsClone.value);
  },
  { deep: true, immediate: true },
);

// 初始化表单
const initForm = () => {
  props.fields.forEach((col) => {
    if (props.initialValues && props.initialValues[col.prop] !== undefined) {
      formState[col.prop] = props.initialValues[col.prop];
    } else if (col.defaultValue !== undefined) {
      formState[col.prop] = col.defaultValue;
    } else {
      if (col.type === "select" && col.mode === "multiple") {
        formState[col.prop] = [];
      } else {
        formState[col.prop] = undefined;
      }
    }
  });
};

// 处理字段变化
const handleFieldChange = (col: FieldConfig, e: any) => {
  if (col.on && col.on.change) {
    switch (col.type) {
      case "radio":
        col.on.change(e.target.value);
        break;
      // case "select":
      //   col.on.change(e);
      //   break;
      // case "datePicker":
      //   col.on.change(e.target.value);
      //   break;
      default:
        col.on.change(e);
        break;
    }
  }
};

// 处理字段清空
const handleFieldClear = (col: FieldConfig) => {
  if (col.on && col.on.clear) {
    col.on.clear();
  }
};

// 提交搜索
// const handleSubmit = () => {
//   emit("search", {
//     ...formState,
//     timeRange: timeRange.value,
//   });
// };

// 重置表单
// const resetForm = () => {
//   initForm();
//   timeRange.value = props.defaultTimeRange || "year";
//   emit("reset");
// };

// 初始化
initForm();

// 监听初始值变化
watch(
  () => props.initialValues,
  () => {
    initForm();
  },
  { deep: true },
);
// 监听字段选项的变化，当选项数组发生变化时自动清空当前值。这需要修改  SearchForm 组件的逻辑。
watch(
  () => props.fields,
  () => {
    initForm();
  },
  { deep: true },
);
// 监听字段选项的变化，当选项数组发生变化时自动清空当前值
watch(
  () => props.fields.map((field) => field.options),
  (newOptions, oldOptions) => {
    if (oldOptions) {
      props.fields.forEach((field, index) => {
        // 只有当选项真正变化且不是由于初始化引起的变化才清空
        if (
          field.type === "select" &&
          JSON.stringify(newOptions[index]) !== JSON.stringify(oldOptions[index]) &&
          formState[field.prop] !== undefined
        ) {
          // 检查当前值是否在新选项中存在
          const currentValue = formState[field.prop];
          const newOpts = field.options || [];
          const valueExists = newOpts.some(
            (opt: any) =>
              opt.value === currentValue ||
              (Array.isArray(currentValue) && currentValue.includes(opt.value)),
          );

          // 只有当当前值在新选项中不存在时才清空
          if (!valueExists) {
            formState[field.prop] = field.mode === "multiple" ? [] : undefined;
          }
        }
      });
    }
  },
  { deep: true },
);
</script>

<style scoped lang="less">
.search-form-container {
  padding: 16px 16px 0;
  // background: rgba(0, 0, 0, 0.1);
  // border-radius: 8px;
  // margin-bottom: 24px;

  .search-form-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .search-left {
      flex: 1;
      margin-right: 24px;

      :deep(.ant-form-item) {
        margin-bottom: 16px;
        width: 100%;

        .ant-form-item-label {
          width: 80px;
          text-align: right;

          label {
            color: #fff;
          }
        }

        .ant-form-item-control {
          flex: 1;
        }
      }

      :deep(.ant-col) {
        min-width: 25%;
      }
    }

    .search-right {
      width: fit-content;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 16px; // 右侧多个字段之间的间距

      .right-field-item {
        display: flex;
        align-items: center;

        // 右侧表单项样式
        :deep(.ant-form-item) {
          margin-bottom: 0;

          .ant-form-item-label {
            width: auto;
            text-align: right;
            margin-right: 8px;

            label {
              color: #fff;
            }
          }

          .ant-form-item-control {
            flex: none;
          }
        }
      }
    }
    .search-col {
      .ant-row.ant-form-item {
        display: flex;
        align-items: center;
      }
    }
  }

  .search-actions {
    text-align: left;
  }
}
</style>

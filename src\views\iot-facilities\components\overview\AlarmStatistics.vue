<template>
  <PageCard :title="'报警统计'">
    <div class="alarm-statistics">
      <div class="left">
        <div class="item">
          <div class="img">
            <img src="@/assets/pages-icon/iot-facilities/ljbjs.png" alt="" />
          </div>
          <div class="text">
            <div class="title">累计报警数</div>
            <div class="number-box">
              <div class="number">100</div>
              <div class="unit">个</div>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="img">
            <img src="@/assets/pages-icon/iot-facilities/ljgzs.png" alt="" />
          </div>
          <div class="text">
            <div class="title">累计故障数</div>
            <div class="number-box">
              <div class="number">100</div>
              <div class="unit">个</div>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="img">
            <img src="@/assets/pages-icon/iot-facilities/ljcls.png" alt="" />
          </div>
          <div class="text">
            <div class="title">累计处理数</div>
            <div class="number-box">
              <div class="number">100</div>
              <div class="unit">个</div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <common-chart :echart-obj="chartOption" />
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import CommonChart from "@/components/CommonChart.vue";

import { ref } from "vue";
const chartOption = ref<any>({
  color: [
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#04F9FA",
        },
        {
          offset: 1,
          color: "#04f9fa80",
        },
      ],
      global: false,
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#0AA2F8",
        },
        {
          offset: 1,
          color: "#0AA2F880",
        },
      ],
      global: false,
    },
    {
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: "#FF9800",
        },
        {
          offset: 1,
          color: "#FF980080",
        },
      ],
      global: false,
    },
  ],
  // 提示框配置
  tooltip: {
    trigger: "axis", // 坐标轴触发
    axisPointer: {
      type: "shadow", // 阴影指示器
    },
    textStyle: {
      // color: "#fff",
      fontSize: "0.25rem",
    },
    formatter: function (params: any) {
      // 自定义提示框格式
      let result = params[0].name + "<br/>";
      params.forEach((item: any) => {
        result += item.marker + item.seriesName + ": " + item.value + "<br/>";
      });
      return result;
    },
  },
  // 图例配置
  legend: {
    bottom: "0%", // 图例位置
    itemHeight: 14,
    itemWidth: 14,
    textStyle: {
      fontSize: "0.14rem",
      color: "#fff",
    },
  },
  // 图表网格配置
  grid: {
    left: "3%", // 左边距
    right: "4%", // 右边距
    bottom: "13%", // 下边距
    top: "15%", // 上边距，为图例留空间
    containLabel: true, // 包含坐标轴标签
  },
  // // X轴配置
  xAxis: {
    type: "category", // 类目轴
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
      // 标签格式化函数，处理过长的标签
      formatter: function (value: string) {
        if (value.length > 8) {
          return value.substring(0, 5) + "..."; // 超过5个字符显示省略号
        }
        return value;
      },
    },
    data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
  },

  yAxis: {
    type: "value",
    name: "单位：个",
    nameGap: 30,
    nameTextStyle: {
      fontSize: "0.2rem",
      color: "#FFFFFF",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    axisLabel: {
      color: "#fff",
      fontSize: "0.2rem",
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)",
        // type: "dashed",
      },
    },
  },
  series: [
    {
      name: "Direct",
      type: "bar",
      emphasis: {
        focus: "series",
      },
      backgroundStyle: {
        color: "rgba(255, 255, 255, 0.3)",
      },
      data: [320, 332, 301, 334, 390, 330, 320],
    },
    {
      name: "Email",
      type: "bar",
      stack: "Ad",
      emphasis: {
        focus: "series",
      },
      data: [120, 132, 101, 134, 90, 230, 210],
    },
    {
      name: "Union Ads",
      type: "bar",
      stack: "Ad",
      emphasis: {
        focus: "series",
      },
      data: [220, 182, 191, 234, 290, 330, 310],
    },
  ],
});
</script>

<style lang="less" scoped>
.page-card {
  height: 480px;
  color: #fff;
  .alarm-statistics {
    display: flex;
    height: 100%;
    padding-top: 24px;
    .left {
      flex: 1;
      display: flex;
      flex-flow: column;
      justify-content: space-around;
      align-items: center;
      font-size: 18px;
      .item {
        width: 233.12px;
        height: 96.53px;
        border-radius: 6px;
        border: 1.5px solid #03a4ff;
        background: #03a4ff33;
        display: flex;
        align-items: center;
        padding: 8px 16px;
        .img {
          width: 43.5px;
          height: 43.5px;
          margin-right: 32px;
        }
        .text {
          display: flex;
          flex-flow: column;
          // justify-content: flex-end;
          align-items: flex-end;
          .title {
            font-size: 22px;
          }
          .number-box {
            display: flex;
            align-items: flex-end;
            .number {
              font-size: 35px;
              font-weight: bold;
              margin-right: 8px;
            }
            .unit {
              font-size: 18px;
            }
          }
        }
      }
    }
    .right {
    }
    .chart-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 918px;
      > div {
        flex: 1;
      }
    }
  }
}
</style>

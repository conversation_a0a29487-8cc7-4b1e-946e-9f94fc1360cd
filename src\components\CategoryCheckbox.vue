<template>
  <div class="category-checkbox">
    <div v-for="(group, groupKey) in categoryData" :key="groupKey" class="category-group">
      <div class="group-title">{{ groupKey }}</div>
      <div v-for="(item, index) in group" :key="index" class="checkbox-item">
        <input
          type="checkbox"
          :id="`${groupKey}-${index}`"
          :checked="item.checked"
          @change="handleCheck(groupKey, index, $event.target.checked)"
        />
        <label :for="`${groupKey}-${index}`">{{ item.label }}</label>
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义 props
defineProps({
  categoryData: {
    type: Object,
    required: true,
    default: () => ({
      冷源: [
        { label: "B2冷冻机房", checked: true },
        { label: "B1循环泵机房", checked: true },
        { label: "A栋RF冷却塔", checked: true },
      ],
      热源: [{ label: "B1锅炉", checked: true }],
      空调: [
        { label: "空调箱", checked: true },
        { label: "新风机", checked: true },
        { label: "风机盘管", checked: true },
      ],
      管路: [
        { label: "空调水管", checked: true },
        { label: "空调风管", checked: true },
      ],
    }),
  },
});

// 定义 emits
const emit = defineEmits(["check-change"]);

// 处理勾选事件
const handleCheck = (groupKey, index, isChecked) => {
  // 发射事件给父组件
  emit("check-change", { groupKey, index, isChecked });
  // 注意：在Vue3中直接修改props是不推荐的，这里保留原逻辑仅作演示
  // 实际项目中建议通过父组件更新数据
  // props.categoryData[groupKey][index].checked = isChecked;
};
</script>

<style scoped>
.category-checkbox {
  background-color: none; /* 深色背景，可根据实际调整 */
  color: #fff;
  padding: 10px;
}
.category-group {
  margin-bottom: 15px;
}
.group-title {
  font-weight: bold;
  margin-bottom: 5px;
}
.checkbox-item {
  margin: 5px 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
}
input[type="checkbox"] {
  margin-right: 8px;
}
</style>

import { EnergyRequest } from "@/utils/request";
import api from "@/utils/request";
import type { TimeModel } from "@/models/global";
import type {
  ApiResponse,
  DeviceOverviewItem,
  DeviceStatusListParams,
  DeviceStatusListResponse,
  EnergyOverviewParams,
  EnergyOverviewResponse,
} from "@/models/energy";

/**
 * 获取设备概览
 * @param params 查询参数
 * @returns Promise<DeviceOverviewResponse>
 */
export const getDeviceOverview = () =>
  api.post<ApiResponse<DeviceOverviewItem[]>>("/shibomameng/energyManagement/equipment_Overview");

/**
 * 获取设备状态列表
 * @param params 查询参数
 * @returns Promise<DeviceStatusListResponse>
 */
export const getDeviceStatusList = (params?: DeviceStatusListParams) =>
  EnergyRequest.post<DeviceStatusListResponse>(
    "/shibomameng/energyManagement/equipment_statuslist",
    params || {},
  );

/**
 * 能耗概览
 * @param params 查询参数
 * @returns Promise<DeviceStatusListResponse>
 */
export const getEneryOverview = (params?: EnergyOverviewParams) =>
  EnergyRequest.post<ApiResponse<EnergyOverviewResponse>>(
    "/shibomameng/energyManagement/energyOverview",
    params || {},
  );

/**
 * 能耗趋势
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
export const getEnergyTrend = (params?: EnergyOverviewParams) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/energyConsumptionTrend",
    params || {},
  );

/**
 * 用电趋势
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
export const getElectricityTrend = (params?: EnergyOverviewParams) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/ele_energyConsumptionTrend",
    params || {},
  );

/**
 * 用电结构
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
export const getElectricityStructure = (params?: EnergyOverviewParams) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/ele_energyCountBy4Item",
    params || {},
  );
/**
 * 用电趋势
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
export const getElectricityStructureDetail = (params?: TimeModel) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/ele_tenantElectricityTrends",
    params || {},
  );

/**
 * 能耗趋势（桑基图）
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */

export const getEnergyConsumptionTrend = (params?: TimeModel) =>
  EnergyRequest.post<ApiResponse>("/shibomameng/energyManagement/ele_energyFlow", params || {});

/**
 * 能耗趋势详情（桑基图）
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */

export const getEnergyConsumptionTrendDetail = (params?: TimeModel) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/ele_energyFlowDetail",
    params || {},
  );

/**
 * 用水结构
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
export const getWaterStructure = (params?: TimeModel) =>
  EnergyRequest.post<ApiResponse>(
    "/shibomameng/energyManagement/water_analysisByStructure",
    params || {},
  );

/**
 * 夜间用水
 * @param params 查询参数 { buildId, floor, queryDate }
 * @returns Promise<ApiResponse>
 */
export const getNightWater = (params: { buildId: string; floor: string; queryDate: string }) =>
  EnergyRequest.post<ApiResponse>("/shibomameng/energyManagement/water_analysisByNight", params);

// // 其他能源管理相关接口可以在这里继续添加

// /**
//  * 获取能耗统计数据
//  * @param params 查询参数
//  */
// export const getEnergyConsumption = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>("/shibomameng/energyManagement/energy_consumption", params);

// /**
//  * 获取设备运行状态统计
//  * @param params 查询参数
//  */
// export const getDeviceRunningStats = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/device_running_stats",
//     params,
//   );

// /**
//  * 获取能效分析数据
//  * @param params 查询参数
//  */
// export const getEnergyEfficiencyAnalysis = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/energy_efficiency_analysis",
//     params,
//   );

// /**
//  * 获取设备告警信息
//  * @param params 查询参数
//  */
// export const getDeviceAlarms = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>("/shibomameng/energyManagement/device_alarms", params);

// /**
//  * 获取设备维护记录
//  * @param params 查询参数
//  */
// export const getDeviceMaintenanceRecords = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/device_maintenance_records",
//     params,
//   );

// /**
//  * 更新设备状态
//  * @param params 更新参数
//  */
// export const updateDeviceStatus = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/update_device_status",
//     params,
//   );

// /**
//  * 添加设备维护记录
//  * @param params 维护记录参数
//  */
// export const addMaintenanceRecord = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/add_maintenance_record",
//     params,
//   );

// /**
//  * 获取实时能耗数据
//  * @param params 查询参数
//  */
// export const getRealTimeEnergyData = (params: any) =>
//   EnergyRequest.post<ApiResponse<any>>(
//     "/shibomameng/energyManagement/realtime_energy_data",
//     params,
//   );

<template>
  <PageCard :title="'能耗概览'">
    <div class="nenghao-overview">
      <div>
        <div class="item" v-for="item in energyOverviewList" :key="item.value">
          <div>
            <div>
              <span class="fz30">{{ item.value }}</span>
              <span class="unit">{{ item.valueUnit }}</span>
            </div>
            <div>{{ item.timeUnitText }}</div>
          </div>
          <div v-if="item.trend">
            <div>
              <span class="fz30">{{ item.trend }}</span>
              <span class="unit">{{ item.trendUnit }}</span>
              <span class="up" v-if="item.trend > 0">
                <img src="@/assets/pages-icon/energy/up_arrow.png" alt="" srcset="" />
              </span>
              <span class="down" v-else-if="item.trend < 0">
                <img src="@/assets/pages-icon/energy/down_arrow.png" alt="" srcset="" />
              </span>
            </div>
            <div>今日趋势同比</div>
          </div>
          <div></div>
        </div>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
import { getEneryOverview } from "@/api/energy";
import { ref, onMounted } from "vue";
import { EnergyOverviewResponse } from "@/models/energy";
// 这个接口根据参数需要调用6次，后端说的，一次太卡了 超时
// 定义所有需要调用的参数组合
const params = [
  { timeModel: "currentDay", energyType: "electricity" },
  { timeModel: "currentMonth", energyType: "electricity" },
  { timeModel: "currentYear", energyType: "electricity" },
  { timeModel: "currentDay", energyType: "water" },
  { timeModel: "currentMonth", energyType: "water" },
  { timeModel: "currentYear", energyType: "water" },
];

// 使用Promise.all并行调用所有接口
const getAllEnergyData = () => {
  const promises = params.map((param) => getEneryOverview(param));
  return Promise.all(promises)
    .then((results) => {
      return results;
    })
    .catch((error) => {
      console.error("接口调用出错", error);
      throw error; // 可以根据需要处理错误
    });
};
const energyOverviewList = ref<EnergyOverviewResponse[]>([]);
const timeUnitText = [
  "今日用电量",
  "本月用电量",
  "本年用电量",
  "今日用水量",
  "本月用水量",
  "本年用水量",
];
onMounted(() => {
  getAllEnergyData()
    .then((data) => {
      data.forEach((item) => {
        energyOverviewList.value.push(item.data);
      });
      energyOverviewList.value.forEach((item, index) => {
        item.timeUnitText = timeUnitText[index];
      });
    })
    .catch((error) => {
      console.error("获取数据失败:", error);
    });
});
</script>

<style lang="less" scoped>
.nenghao-overview {
  color: #fff;
  font-size: 22px;
  > div {
    display: flex;
    flex-wrap: wrap;
  }
  .item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px;
    text-align: center;
    width: 400px;
    height: 140px;
  }
  .item::after {
    width: 2px;
    height: 80%;
    position: absolute;
    right: 0px;
    top: 10%;
    content: "";
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      #ccc 48.96%,
      rgba(255, 255, 255, 0)
    );
  }
  .item:nth-child(3n)::after {
    display: none !important;
  }
  .up {
    color: red;
    font-weight: 900;
    line-height: 50px;
    font-size: 30px;
    margin-left: 10px;
  }
  .down {
    color: green;
    font-weight: 900;
    line-height: 50px;
    font-size: 30px;
    margin-left: 10px;
  }
  .unit {
    margin: 0 12px;
  }
}
.fz16 {
  font-size: 16px;
}
.fz20 {
  font-size: 20px;
}
.fz26 {
  font-size: 26px;
}
.fz30 {
  font-size: 30px;
}
.fz36 {
  font-size: 36px;
}
</style>

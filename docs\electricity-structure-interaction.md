# 用电结构双层圆环图交互功能

## 功能概述

用电结构页面实现了左右两个图表的联动交互功能：
- **左侧圆环图**：显示用电类型总览（照明、空调、动力、其他）
- **右侧双层圆环图**：根据左侧选择的分项，动态显示对应的详细数据

## 交互流程

### 1. 初始状态
- 左侧显示用电类型总览圆环图
- 右侧显示空状态，提示用户"点击左侧分项查看详细数据"
- 左侧图表底部有动画提示"点击查看详情"

### 2. 点击交互
- 用户点击左侧圆环图的任意分项（如"照明"）
- 右侧立即显示对应分项的双层圆环图
- 顶部出现"返回总览"按钮

### 3. 双层圆环图结构
- **内环**：显示该分项的主要子分类（如照明的LED灯具、传统灯具）
- **外环**：显示该分项的详细子分类（如办公区照明、走廊照明等）
- **中心标题**：显示当前选中的分项名称

### 4. 返回功能
- 点击"返回总览"按钮可回到初始状态
- 右侧重新显示空状态

## 数据结构

每个分项包含以下数据结构：

```typescript
{
  total: number,           // 总占比
  innerData: [             // 内环数据
    { name: string, value: number, color: string }
  ],
  outerData: [             // 外环数据  
    { name: string, value: number, color: string }
  ]
}
```

## 当前配置的分项数据

### 照明 (45%)
- **内环**：LED灯具(25%)、传统灯具(20%)
- **外环**：办公区照明(15%)、走廊照明(10%)、景观照明(12%)、应急照明(8%)

### 空调 (30%)
- **内环**：中央空调(18%)、分体空调(12%)
- **外环**：冷热站(10%)、空调末端(8%)、新风系统(7%)、排风系统(5%)

### 动力 (15%)
- **内环**：电梯设备(8%)、水泵设备(7%)
- **外环**：客梯(5%)、货梯(3%)、给水泵(4%)、排水泵(3%)

### 其他 (10%)
- **内环**：办公设备(6%)、安防设备(4%)
- **外环**：计算机(3%)、打印设备(3%)、监控系统(2%)、门禁系统(2%)

## 视觉特性

### 颜色方案
- 使用渐变色彩，提升视觉效果
- 内外环使用不同色系，便于区分
- 保持整体色调协调统一

### 动画效果
- 图表切换有平滑过渡动画
- 鼠标悬停有高亮效果
- 点击提示有呼吸动画

### 响应式设计
- 适配不同屏幕尺寸
- 标签文字自动换行
- 图表大小自适应

## 技术实现要点

1. **事件监听**：通过 `@chartclick` 监听图表点击事件
2. **动态配置**：使用 `computed` 属性动态生成右侧图表配置
3. **状态管理**：使用 `ref` 管理当前选中的分项
4. **条件渲染**：根据选中状态显示不同内容

## 扩展建议

1. **数据联动**：可以根据时间范围（今日/本月/本年）动态更新数据
2. **钻取功能**：可以继续点击右侧图表进入更深层级的数据
3. **数据导出**：添加数据导出功能
4. **对比分析**：支持多个分项的对比显示
5. **实时数据**：接入实时数据源，动态更新图表

<template>
  <PageCard :title="'故障报警'">
    <div class="fault-alarm">
      <div class="search-box">
        <a-space :size="30">
          <div>
            <span>设备类型：</span>
            <a-select
              v-model:value="status"
              style="width: 120px"
              :options="options1"
              size="large"
              @change="handleChange"
            ></a-select>
          </div>
          <div>
            <span>报警类型：</span>
            <a-select
              v-model:value="build"
              style="width: 120px"
              :options="options2"
              size="large"
              @change="handleChange"
            ></a-select>
          </div>
          <div>
            <span>是否解除：</span>
            <a-select
              v-model:value="floor"
              size="large"
              style="width: 120px"
              :options="options3"
            ></a-select>
          </div>
        </a-space>
        <a-radio-group v-model:value="timer" button-style="solid" size="large" class="time">
          <a-radio-button value="a">今日</a-radio-button>
          <a-radio-button value="b">本周</a-radio-button>
          <a-radio-button value="c">本月</a-radio-button>
        </a-radio-group>
      </div>
      <div class="table-box">
        <BaseTable
          class="ant-table-striped"
          :columns="columns"
          :data-source="data"
          :row-class-name="(_record:any, index:number) => (index % 2 === 1 ? 'table-dan' : 'table-shuang')"
          :pagination="false"
          :bordered="false"
        >
          <template v-slot:column="scoped">
            <a-row
              v-if="scoped.column.dataIndex === 'operation'"
              class="operation"
              align="middle"
              justify="center"
            >
              <!-- <a-button type="primary" size="small" @click="toDetails(scoped.record)">
            </a-button> -->
              <img src="@/assets/pages-icon/energy/search1.png" alt="" srcset="" />
              <img src="@/assets/pages-icon/energy/place.png" alt="" srcset="" />
            </a-row>
          </template>
        </BaseTable>
      </div>
    </div>
  </PageCard>
</template>

<script setup lang="ts">
import PageCard from "@/components/PageCard.vue";
// import SearchForm from "@/components/SearchForm.vue";
import BaseTable from "@/components/BaseTable.vue";
// import { ContainerOutlined } from "@ant-design/icons-vue";
import { ref } from "vue";
import type { SelectProps } from "ant-design-vue";

const columns = [
  { title: "表具编号", dataIndex: "numId" },
  { title: "设备名称", dataIndex: "name" },
  { title: "位置", dataIndex: "address" },
  { title: "在线/离线", dataIndex: "online" },
  { title: "操作", dataIndex: "operation" },
];
const data = [
  {
    key: "1",
    number: 1,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "2",
    number: 2,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "3",
    number: 3,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
  {
    key: "4",
    number: 4,
    numId: 5446851,
    name: "3层5号表",
    address: "C栋3层",
    online: "离线",
  },
];
// const toDetails = (i: any) => {
//   console.log("%c Line:99 🍋 i", "color:#f5ce50", i);
// };

// 表单相关 status

const status = ref("");
const build = ref("");
const floor = ref("");
const timer = ref("a");
const options1 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "在线", value: "online" },
  { label: "离线", value: "offline" },
]);
const options2 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "A栋", value: "A" },
  { label: "b栋", value: "b" },
  { label: "c栋", value: "c" },
  { label: "d栋", value: "d" },
]);
const options3 = ref<SelectProps["options"]>([
  { label: "全部", value: "" },
  { label: "1楼", value: "1" },
  { label: "2楼", value: "2" },
]);
const handleChange = () => {
  console.log("view handleChange");
};
</script>

<style lang="less" scoped>
.page-card {
  height: 848px;
  .search-box {
    height: 88px;
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    color: #fff;
    span {
      margin-right: 10px;
    }
  }
  .table-box {
    position: relative;
    height: 650px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 16px;
    // border: 1px solid rgba(255, 255, 255, 0.1);
    // border-radius: 4px;
  }

  // .ant-table-striped {
  //   height: 100%;
  // }

  :deep .ant-table-striped {
    position: relative;
    .ant-table.ant-table-small .ant-table-body {
      overflow-y: hidden !important;
    }
    .table-dan {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(7, 27, 51, 1);
        color: #fff;
      }
    }
    .table-shuang {
      height: 57px;
      margin: 8px;
      td {
        background-color: rgba(15, 36, 61, 1);
        color: #fff;
      }
    }
    .ant-table-thead {
      th {
        background-color: rgba(15, 41, 82, 1);
        color: #fff;
        height: 67px;
      }
    }
  }

  img {
    margin-right: 20px;
    cursor: pointer;
  }
  img:hover {
    opacity: 0.7;
  }
  // .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar-thumb) {
  //   background-color: rgba(255, 255, 255, 0.2);
  //   border-radius: 4px;
  // }

  // .ant-table-striped :deep(.ant-table-body::-webkit-scrollbar-track) {
  //   background-color: rgba(0, 0, 0, 0.1);
  //   border-radius: 4px;
  // }
}
</style>

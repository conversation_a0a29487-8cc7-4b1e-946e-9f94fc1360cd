// 能源管理相关类型定义

/** 通用API响应结构 */
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

/** 设备概览数据 */
export interface DeviceOverviewItem {
  id?: string | number;
  buildId: string | number;
  buildName: string;
  ele: { total: number; online: number };
  water: { total: number; online: number };
  [key: string]: any; // 允许其他自定义字段
}

/** 设备状态列表请求入参 */
export interface DeviceStatusListParams {
  deviceStatus: string; //设备状态：1-未激活，2-禁用，3-在线，4-离线
  build: string; //建筑
  floor: string; //楼层
  pageNum: number;
  pageSize: number;
  deviceType?: null; //1电表2水表?
}
/** 设备状态列表数据类型 */
export interface DeviceStatusItem {
  id: string | number;
  gatewIp: null;
  measurementArea: null;
  build: string; //建筑
  locationFloor: void; //楼层
  room: string; //房间
  instrumentModel: null; //仪表类型
  instrumentNum: null; //仪表唯一编号
  cabinetNumber: null; //柜号
  smsLocation: null; //安装位置
  itemCode: null; //分项
  parentCode: null;
  deviceType: null; //1电表2水表
  status: number; //1-未激活，2-禁用，3-在线，4-离线
  [key: string]: any; // 允许其他字段
}

export interface DeviceStatusListResponse {
  code: number;
  msg: string;
  total: number;
  rows: DeviceStatusItem[];
}

// 能耗概览入参及结果
export interface EnergyOverviewParams {
  timeModel: string;
  energyType: string;
}
export interface EnergyOverviewResponse {
  trend: number; //趋势
  valueUnit: string; //值单位
  value: string; //值
  trendUnit: string; //趋势单位
  timeUnitText?: string;
}

/** 楼宇楼层数据类型 */
export interface BuildFloorItem {
  buildId: string;
  buildName: string;
  floors: Array<{
    floorId: string;
    floorName: string;
  }>;
}

export interface BuildFloorResponse {
  code: number;
  msg: string;
  data: BuildFloorItem[];
}

// /** 通用API响应结构 */
// export interface ApiResponse<T = any> {
//   code: number;
//   message: string;
//   data: T;
//   success: boolean;
// }

// /** 设备概览数据类型 */
// export interface EquipmentOverviewItem {
//   id: string | number;
//   name: string;
//   type: string;
//   status: 'online' | 'offline' | 'fault' | 'maintenance';
//   location: string;
//   power?: number; // 功率
//   energy?: number; // 能耗
//   efficiency?: number; // 效率
//   lastUpdateTime: string;
//   [key: string]: any; // 允许其他自定义字段
// }

// export interface EquipmentOverviewResponse {
//   total: number;
//   online: number;
//   offline: number;
//   fault: number;
//   maintenance: number;
//   equipmentList: EquipmentOverviewItem[];
// }

// /** 设备状态列表数据类型 */
// export interface EquipmentStatusItem {
//   id: string | number;
//   deviceId: string;
//   deviceName: string;
//   deviceType: string;
//   status: 'normal' | 'warning' | 'error' | 'offline';
//   statusText: string;
//   location: string;
//   building?: string;
//   floor?: string;
//   room?: string;
//   parameters: {
//     temperature?: number;
//     humidity?: number;
//     pressure?: number;
//     voltage?: number;
//     current?: number;
//     power?: number;
//     [key: string]: any;
//   };
//   alarmLevel?: 'low' | 'medium' | 'high' | 'critical';
//   alarmMessage?: string;
//   lastCheckTime: string;
//   nextMaintenanceTime?: string;
// }

// export interface EquipmentStatusListResponse {
//   total: number;
//   pageSize: number;
//   currentPage: number;
//   totalPages: number;
//   statusSummary: {
//     normal: number;
//     warning: number;
//     error: number;
//     offline: number;
//   };
//   equipmentList: EquipmentStatusItem[];
// }

// /** 设备概览请求参数 */
// export interface EquipmentOverviewParams {
//   buildingId?: string | number;
//   floorId?: string | number;
//   deviceType?: string;
//   status?: string;
//   startTime?: string;
//   endTime?: string;
// }

// /** 设备状态列表请求参数 */
// export interface EquipmentStatusListParams {
//   page?: number;
//   pageSize?: number;
//   buildingId?: string | number;
//   floorId?: string | number;
//   deviceType?: string;
//   status?: string;
//   keyword?: string; // 搜索关键词
//   sortBy?: string;
//   sortOrder?: 'asc' | 'desc';
//   startTime?: string;
//   endTime?: string;
// }

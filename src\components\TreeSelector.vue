<template>
  <div class="tree-selector">
    <div v-for="node in treeData" :key="node.id" class="tree-node">
      <div class="node-content">
        <!-- 复选框 -->
        <input
          v-if="mode === 'checkbox'"
          type="checkbox"
          :checked="isChecked(node)"
          @change="
            handleCheckChange(node, ($event as Event & { target: HTMLInputElement }).target.checked)
          "
          class="node-checkbox"
        />
        <!-- 单选框 -->
        <input
          v-else-if="mode === 'radio'"
          type="radio"
          :name="radioGroupName"
          :checked="selectedId === node.id"
          @change="handleRadioChange(node)"
          class="node-radio"
        />

        <span class="node-label">{{ node.label }}</span>

        <!-- 展开/折叠按钮 -->
        <button
          v-if="node.children && node.children.length"
          @click="toggleExpand(node)"
          class="toggle-btn"
        >
          {{ node.expanded ? "-" : "+" }}
        </button>
      </div>

      <!-- 子节点 -->
      <div v-if="node.children && node.children.length && node.expanded" class="children-node">
        <TreeSelector
          :tree-data="node.children"
          :mode="mode"
          :selected-ids="selectedIds"
          :selected-id="selectedId"
          @update:selectedIds="handleChildCheck"
          @update:selectedId="handleChildRadio"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

// 定义节点类型
interface TreeDataNode {
  id: string | number;
  label: string;
  children?: TreeDataNode[];
  expanded?: boolean; // 控制是否展开
}

// 定义属性
const props = defineProps({
  treeData: {
    type: Array as () => TreeDataNode[],
    required: true,
  },
  mode: {
    type: String as () => "checkbox" | "radio",
    default: "checkbox",
  },
  selectedIds: {
    type: Array as () => (string | number)[],
    default: () => [],
  },
  selectedId: {
    type: [String, Number],
    default: null,
  },
});

// 定义事件
const emit = defineEmits<{
  (e: "update:selectedIds", value: (string | number)[]): void;
  (e: "update:selectedId", value: string | number | null): void;
}>();

// 单选组名称，确保同一组单选按钮互斥
const radioGroupName = computed(() => `tree-radio-${Math.random().toString(36).substr(2, 9)}`);

// 检查节点是否被选中
const isChecked = (node: TreeDataNode) => {
  return props.selectedIds.includes(node.id);
};

// 处理复选框变化
const handleCheckChange = (node: TreeDataNode, checked: boolean) => {
  let newSelectedIds = [...props.selectedIds];

  if (checked) {
    // 添加当前节点ID
    if (!newSelectedIds.includes(node.id)) {
      newSelectedIds.push(node.id);
    }
    // 递归添加子节点ID
    if (node.children && node.children.length) {
      const childIds = getAllChildIds(node);
      newSelectedIds = [...new Set([...newSelectedIds, ...childIds])];
    }
  } else {
    // 移除当前节点ID
    newSelectedIds = newSelectedIds.filter((id) => id !== node.id);
    // 递归移除子节点ID
    if (node.children && node.children.length) {
      const childIds = getAllChildIds(node);
      newSelectedIds = newSelectedIds.filter((id) => !childIds.includes(id));
    }
  }

  emit("update:selectedIds", newSelectedIds);
};

// 处理单选框变化
const handleRadioChange = (node: TreeDataNode) => {
  emit("update:selectedId", node.id);
};

// 处理子节点复选变化
const handleChildCheck = (childSelectedIds: (string | number)[]) => {
  emit("update:selectedIds", childSelectedIds);
};

// 处理子节点单选变化
const handleChildRadio = (childSelectedId: string | number | null) => {
  emit("update:selectedId", childSelectedId);
};

// 切换展开/折叠状态
const toggleExpand = (node: TreeDataNode) => {
  node.expanded = !node.expanded;
};

// 获取所有子节点ID
const getAllChildIds = (node: TreeDataNode): (string | number)[] => {
  let ids: (string | number)[] = [];
  if (node.children && node.children.length) {
    node.children.forEach((child) => {
      ids.push(child.id);
      ids = [...ids, ...getAllChildIds(child)];
    });
  }
  return ids;
};
</script>

<style scoped lang="less">
.tree-selector {
  padding-left: 10px;

  .tree-node {
    margin: 5px 0;

    .node-content {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 3px 0;

      .node-label {
        color: #fff;
        cursor: pointer;
      }

      .toggle-btn {
        width: 16px;
        height: 16px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: #333;
        color: white;
        border: none;
        cursor: pointer;
      }
    }

    .children-node {
      padding-left: 20px;
      border-left: 1px dashed #666;
      margin-left: 5px;
    }
  }
}
</style>
